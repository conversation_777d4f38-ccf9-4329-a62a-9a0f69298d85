{"name": "frederico/jerseycolours", "license": "proprietary", "type": "project", "autoload": {"psr-4": {"": "src/", "SymfonyStandard\\": "app/SymfonyStandard/"}}, "require": {"php": ">=5.3.9", "symfony/symfony": "2.7.*", "doctrine/orm": "~2.2,>=2.2.3,<2.5", "doctrine/dbal": "<2.5", "doctrine/doctrine-bundle": "~1.4", "symfony/assetic-bundle": "~2.3", "symfony/swiftmailer-bundle": "~2.3", "symfony/monolog-bundle": "~2.4", "sensio/distribution-bundle": "~4.0", "sensio/framework-extra-bundle": "~3.0,>=3.0.2", "incenteev/composer-parameter-handler": "~2.0", "doctrine/doctrine-fixtures-bundle": "^2.2", "knplabs/knp-menu-bundle": "~2", "friendsofsymfony/jsrouting-bundle": "@stable", "friendsofsymfony/user-bundle": "1.3.*", "mopa/bootstrap-bundle": "^2.3", "vich/uploader-bundle": "^0.14.0", "twig/extensions": "^1.2", "jakoch/phantomjs-installer": "^1.9.8", "samj/doctrine-sluggable-bundle": "2.0", "friendsofsymfony/rest-bundle": "^1.7", "jms/serializer-bundle": "^1.0", "playbloom/guzzle-bundle": "^1.1", "knplabs/knp-paginator-bundle": "^2.4", "fredcido/phpsvg": "dev-master", "glanchow/doctrine-fuzzy": "dev-master", "knplabs/knp-snappy-bundle": "^1.3", "twig/twig": "^1.35"}, "repositories": [{"type": "vcs", "url": "https://github.com/fredcido/phpsvg"}, {"type": "vcs", "url": "https://github.com/glanchow/doctrine-fuzzy.git"}], "require-dev": {"sensio/generator-bundle": "~2.3"}, "scripts": {"post-root-package-install": ["SymfonyStandard\\Composer::<PERSON>RootPackageInstall"], "post-install-cmd": ["Incenteev\\ParameterHandler\\ScriptHandler::buildParameters", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::buildBootstrap", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::clearCache", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installAssets", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installRequirementsFile", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::removeSymfonyStandardFiles", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::prepareDeploymentTarget", "PhantomInstaller\\Installer::installPhantomJS", "DBLS\\Bundle\\JerseyColoursBundle\\Composer\\StoredProcedure::installLevenshtein"], "post-update-cmd": ["Incenteev\\ParameterHandler\\ScriptHandler::buildParameters", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::buildBootstrap", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::clearCache", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installAssets", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installRequirementsFile", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::removeSymfonyStandardFiles", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::prepareDeploymentTarget", "PhantomInstaller\\Installer::installPhantomJS", "DBLS\\Bundle\\JerseyColoursBundle\\Composer\\StoredProcedure::installLevenshtein"]}, "config": {"bin-dir": "bin"}, "extra": {"symfony-app-dir": "app", "symfony-web-dir": "web", "symfony-assets-install": "relative", "incenteev-parameters": {"file": "app/config/parameters.yml"}}}