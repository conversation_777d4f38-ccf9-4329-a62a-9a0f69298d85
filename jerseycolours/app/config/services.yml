# Learn more about services, parameters and containers at
# http://symfony.com/doc/current/book/service_container.html
parameters:
#    parameter_name: value

services:
    validator.jersey.unique:
        class: DBLS\Bundle\JerseyColoursBundle\Validator\Constraints\UniqueValidator
        arguments: [@doctrine.orm.entity_manager]
        tags:
            - { name: validator.constraint_validator, alias: unique.validator }
    validator.jersey.pattern:
        class: DBLS\Bundle\JerseyColoursBundle\Validator\Constraints\PatternValidator
        arguments: [@doctrine.orm.entity_manager]
        tags:
            - { name: validator.constraint_validator, alias: pattern.validator }
    validator.jersey.fileextension:
        class: DBLS\Bundle\JerseyColoursBundle\Validator\Constraints\FileExtensionValidator
        tags:
            - { name: validator.constraint_validator, alias: fileextension.validator }
    
    form.extension.image_type_extension:
        class: DBLS\Bundle\JerseyColoursBundle\Form\Extension\ImageTypeExtension
        tags:
            - { name: form.type_extension, alias: file }
    twig.extension.text:
        class: Twig_Extensions_Extension_Text
        tags:
            - { name: twig.extension }
