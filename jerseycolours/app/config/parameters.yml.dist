# This file is a "template" of what your parameters.yml file should look like
# Set parameters here that may be different on each deployment target of the app, e.g. development, staging, production.
# http://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration
parameters:
    database_host: 127.0.0.1
    database_port: 3306
    database_name: jockey_colors
    database_user: jockey_user
    database_password: your_password
    mailer_transport: smtp
    mailer_host: 127.0.0.1
    mailer_user: null
    mailer_password: null
    messages:
        success: Operation was a success
        error: Something went wrong
    secret: c8add7d406537a16a91ccd60ddfcd66682d8508e
    svg_server: http://127.0.0.1:9494
    jerseys_dir: %kernel.root_dir%/../web/images/silks
    levenshtein_ratio: 75
