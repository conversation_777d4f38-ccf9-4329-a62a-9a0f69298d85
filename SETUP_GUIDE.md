# 🏇 Jersey Colours - Local Development Setup Guide

This guide will help you set up the Jersey Colours application (Symfony 2.7) on your local machine.

## 📋 Prerequisites

### Required Software
- **PHP 5.6 - 7.1** (PHP 7.1 recommended)
- **MySQL 5.5+** 
- **Composer** (latest version)
- **Node.js** (for SVG generation server)

### System Requirements
- **Memory**: 2GB+ RAM
- **Disk Space**: 1GB+ free space
- **OS**: macOS, Linux, or Windows with WSL

## 🚀 Quick Setup (Automated)

Run the automated setup script:

```bash
./setup_local.sh
```

Follow the prompts to configure your database connection.

## 🔧 Manual Setup

### Step 1: Install PHP 7.1

**macOS (Homebrew):**
```bash
brew install php@7.1
echo 'export PATH="/usr/local/opt/php@7.1/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

**Ubuntu/Debian:**
```bash
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php7.1 php7.1-cli php7.1-mysql php7.1-xml php7.1-mbstring php7.1-curl php7.1-zip php7.1-gd
```

### Step 2: Install MySQL

**macOS:**
```bash
brew install mysql@5.7
brew services start mysql@5.7
```

**Ubuntu/Debian:**
```bash
sudo apt install mysql-server-5.7
sudo systemctl start mysql
```

### Step 3: Install Composer

```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### Step 4: Database Setup

```sql
-- Connect to MySQL
mysql -u root -p

-- Create database
CREATE DATABASE jockey_colors CHARACTER SET utf8 COLLATE utf8_general_ci;

-- Create user (optional)
CREATE USER 'jockey_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON jockey_colors.* TO 'jockey_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Step 5: Configure Application

```bash
cd jerseycolours

# Copy and edit parameters
cp app/config/parameters.yml.dist app/config/parameters.yml

# Edit the database configuration in parameters.yml:
# database_host: 127.0.0.1
# database_name: jockey_colors  
# database_user: jockey_user
# database_password: your_password
```

### Step 6: Install Dependencies

```bash
# Install PHP dependencies (ignore platform requirements for legacy compatibility)
composer install --ignore-platform-reqs
```

### Step 7: Set Up Database

**Option A: Import Complete Database Dump (Recommended)**
```bash
# If you have the complete database dump file 'jockey_colours'
mysql -u jockey_user -p jockey_colors < jockey_colours
```

**Option B: Create Schema and Import Basic Data**
```bash
# Create database schema
php app/console doctrine:schema:create

# Install custom MySQL functions
mysql -u jockey_user -p jockey_colors < documentation/functions.sql

# Import color data
mysql -u jockey_user -p jockey_colors < documentation/data.sql
```

### Step 8: Install Assets & Clear Cache

```bash
# Install web assets
php app/console assets:install web --symlink

# Clear cache
php app/console cache:clear --env=dev

# Set permissions
chmod -R 777 app/cache app/logs web/images
```

## 🌐 Running the Application

### Start the Symfony Server

```bash
cd jerseycolours
php app/console server:run
```

### Start SVG Generation Server (Optional)

```bash
cd web/generator
node server.js
```

### Access the Application

Open your browser to: **http://127.0.0.1:8000**

## 📁 Important Files & Directories

- `app/config/parameters.yml` - Database and application configuration
- `documentation/data.sql` - Color data import
- `documentation/functions.sql` - Custom MySQL functions
- `web/images/` - Jersey pattern images and generated files
- `src/DBLS/Bundle/JerseyColoursBundle/` - Main application code

## 🔍 Troubleshooting

### Common Issues

**1. PHP Version Compatibility**
```bash
# Check PHP version
php -v

# If using wrong version, switch to PHP 7.1
# macOS: brew link php@7.1 --force
# Ubuntu: sudo update-alternatives --set php /usr/bin/php7.1
```

**2. MySQL Connection Issues**
```bash
# Test MySQL connection
mysql -u your_username -p

# Check MySQL service status
# macOS: brew services list | grep mysql
# Ubuntu: sudo systemctl status mysql
```

**3. Permission Issues**
```bash
# Fix file permissions
chmod -R 755 jerseycolours/
chmod -R 777 jerseycolours/app/cache
chmod -R 777 jerseycolours/app/logs
chmod -R 777 jerseycolours/web/images
```

**4. Composer Issues**
```bash
# Clear Composer cache
composer clear-cache

# Reinstall dependencies
rm -rf vendor/
composer install --ignore-platform-reqs
```

**5. Cache Issues**
```bash
# Clear all caches
php app/console cache:clear --env=dev
php app/console cache:clear --env=prod
rm -rf app/cache/*
```

## 🎯 Next Steps

1. **Create Admin User**: Use FOSUserBundle commands to create admin users
2. **Import Data**: Use the import functionality to load jersey data
3. **Configure SVG Server**: Set up the Node.js SVG generation server for jersey rendering
4. **Test Functionality**: Try creating and editing jersey designs

## 📚 Additional Resources

- [Symfony 2.7 Documentation](https://symfony.com/doc/2.7/index.html)
- [Doctrine ORM Documentation](https://www.doctrine-project.org/projects/doctrine-orm/en/2.4/index.html)
- [FOSUserBundle Documentation](https://github.com/FriendsOfSymfony/FOSUserBundle/tree/1.3.x)

## 🆘 Getting Help

If you encounter issues:
1. Check the application logs: `jerseycolours/app/logs/`
2. Verify database connection and data
3. Ensure all required PHP extensions are installed
4. Check file permissions on cache and log directories
