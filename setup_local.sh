#!/bin/bash

# Jersey Colours Local Setup Script
# This script sets up the Jersey Colours application for local development

set -e  # Exit on any error

echo "🏇 Jersey Colours Local Setup Script"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "jerseycolours/composer.json" ]; then
    print_error "Please run this script from the root directory of the project"
    exit 1
fi

# Step 1: Check PHP version
print_status "Checking PHP version..."
PHP_VERSION=$(php -r "echo PHP_VERSION;")
print_status "PHP Version: $PHP_VERSION"

if ! php -v | grep -E "PHP [5-7]\.[0-9]" > /dev/null; then
    print_warning "This application requires PHP 5.6-7.1. Current version might not be compatible."
fi

# Step 2: Check MySQL
print_status "Checking MySQL connection..."
if ! command -v mysql &> /dev/null; then
    print_error "MySQL is not installed or not in PATH"
    exit 1
fi

# Step 3: Database setup
echo ""
echo "📊 Database Configuration"
echo "========================"
read -p "Enter MySQL username (default: root): " DB_USER
DB_USER=${DB_USER:-root}

read -s -p "Enter MySQL password: " DB_PASS
echo ""

read -p "Enter database name (default: jockey_colors): " DB_NAME
DB_NAME=${DB_NAME:-jockey_colors}

# Test MySQL connection
print_status "Testing MySQL connection..."
if ! mysql -u "$DB_USER" -p"$DB_PASS" -e "SELECT 1;" &> /dev/null; then
    print_error "Failed to connect to MySQL with provided credentials"
    exit 1
fi

# Create database if it doesn't exist
print_status "Creating database '$DB_NAME' if it doesn't exist..."
mysql -u "$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8 COLLATE utf8_general_ci;"

# Step 4: Configure parameters.yml
print_status "Creating parameters.yml from template..."
cd jerseycolours

if [ ! -f "app/config/parameters.yml" ]; then
    cp app/config/parameters.yml.dist app/config/parameters.yml
    
    # Update database configuration
    sed -i.bak "s/database_host: .*/database_host: 127.0.0.1/" app/config/parameters.yml
    sed -i.bak "s/database_name: .*/database_name: $DB_NAME/" app/config/parameters.yml
    sed -i.bak "s/database_user: .*/database_user: $DB_USER/" app/config/parameters.yml
    sed -i.bak "s/database_password: .*/database_password: $DB_PASS/" app/config/parameters.yml
    
    # Generate a new secret key
    SECRET=$(openssl rand -hex 32)
    sed -i.bak "s/secret: .*/secret: $SECRET/" app/config/parameters.yml
    
    rm app/config/parameters.yml.bak
    print_status "parameters.yml configured successfully"
else
    print_warning "parameters.yml already exists, skipping configuration"
fi

# Step 5: Install dependencies
print_status "Installing Composer dependencies..."
if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed. Please install Composer first."
    exit 1
fi

# Use --ignore-platform-reqs for legacy compatibility
composer install --ignore-platform-reqs --no-interaction

# Step 6: Import database dump
print_status "Checking for complete database dump..."
if [ -f "../jockey_colours" ]; then
    print_status "Found complete database dump, importing..."
    mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < ../jockey_colours
    print_status "Database dump imported successfully"
else
    print_status "No complete dump found, creating schema and importing basic data..."

    # Create database schema
    print_status "Creating database schema..."
    php app/console doctrine:schema:create --no-interaction

    # Install custom MySQL functions
    print_status "Installing custom MySQL functions (Levenshtein)..."
    mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < ../documentation/functions.sql

    # Import color data
    print_status "Importing color data..."
    mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < ../documentation/data.sql
fi

# Step 7: Install assets
print_status "Installing web assets..."
php app/console assets:install web --symlink

# Step 8: Clear cache
print_status "Clearing cache..."
php app/console cache:clear --env=dev
php app/console cache:clear --env=prod

# Step 9: Set permissions
print_status "Setting proper permissions..."
chmod -R 777 app/cache app/logs web/images

echo ""
print_status "✅ Setup completed successfully!"
echo ""
echo "🚀 To start the application:"
echo "   cd jerseycolours"
echo "   php app/console server:run"
echo ""
echo "🌐 Then open your browser to: http://127.0.0.1:8000"
echo ""
echo "📝 Note: You may need to start the SVG generation server separately:"
echo "   cd web/generator"
echo "   node server.js"
